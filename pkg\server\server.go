package server

import (
	"context"
	"log"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gofrp/fp-multiuser/pkg/authz"
	"github.com/gofrp/fp-multiuser/pkg/config"
	"github.com/gofrp/fp-multiuser/pkg/database"
	"github.com/gofrp/fp-multiuser/pkg/server/controller"
	"github.com/gofrp/fp-multiuser/pkg/service"

	"github.com/gin-gonic/gin"
)

type Server struct {
	cfg *config.Config

	s             *http.Server
	done          chan struct{}
	collectorStop chan struct{}
}

func New(cfg *config.Config) (*Server, error) {
	s := &Server{
		cfg:           cfg,
		done:          make(chan struct{}),
		collectorStop: make(chan struct{}),
	}
	if err := s.init(); err != nil {
		return nil, err
	}
	return s, nil
}

func (s *Server) Run() error {
	bindAddr := s.cfg.GetBindAddress()
	l, err := net.Listen("tcp", bindAddr)
	if err != nil {
		return err
	}
	log.Printf("HTTP server listen on %s", l.Addr().String())
	go func() {
		if err = s.s.Serve(l); err != http.ErrServerClosed {
			log.Printf("error shutdown HTTP server: %v", err)
		}
	}()
	<-s.done
	return nil
}

func (s *Server) Stop() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	// stop background collectors
	select {
	case <-s.collectorStop:
		// already closed
	default:
		close(s.collectorStop)
	}
	if err := s.s.Shutdown(ctx); err != nil {
		log.Fatalf("shutdown HTTP server error: %v", err)
	}
	log.Printf("HTTP server exited")
	close(s.done)
	return nil
}

func (s *Server) init() error {
	// 初始化数据库
	if err := database.Init(s.cfg.Database.DSN); err != nil {
		log.Printf("init database error: %v", err)
		return err
	}

	if err := s.initHTTPServer(); err != nil {
		log.Printf("init HTTP server error: %v", err)
		return err
	}
	return nil
}

func CORS() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"PUT", "PATCH", "DELETE", "GET", "POST"},
		AllowHeaders:     []string{"Origin", "Authorization", "content-type", "x-xsrf-token"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		AllowOriginFunc: func(origin string) bool {
			return true
		},
		MaxAge: 0,
	})
}

func (s *Server) initHTTPServer() error {
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()
	engine.Use(gin.Logger())
	engine.Use(CORS())
	engine.Use(gin.Recovery())

	// Initialize Casbin and attach middleware
	if err := authz.Init(database.DB); err != nil {
		log.Printf("init casbin error: %v", err)
		return err
	}
	// engine.Use(authz.CasbinMiddleware())

	s.s = &http.Server{
		Handler: engine,
	}

	// Reverse proxy: proxy requests under /admin/frps/* to http://127.0.0.1:7500
	targetURL, err := url.Parse("http://127.0.0.1:7500")
	if err != nil {
		return err
	}
	proxy := httputil.NewSingleHostReverseProxy(targetURL)

	engine.Any("/admin/frps/*path", func(c *gin.Context) {
		// Rewrite the path: remove the /admin/frps/ prefix
		p := c.Param("path")
		p = strings.TrimPrefix(p, "/")
		c.Request.URL.Scheme = targetURL.Scheme
		c.Request.URL.Host = targetURL.Host
		c.Request.Host = targetURL.Host
		c.Request.URL.Path = "/api/" + p
		proxy.ServeHTTP(c.Writer, c.Request)
	})

	// Initialize services
	authService := service.NewAuthService(s.cfg.Auth.APIURL)
	userService := service.NewUserService()
	proxyService := service.NewProxyService()
	// Start background traffic collector (poll interval 30s by default)
	trafficService := service.NewTrafficService()
	trafficService.StartCollector(s.cfg.Admin.BaseURL, s.cfg.Admin.Username, s.cfg.Admin.Password, 30*time.Second, s.collectorStop)

	// Register controllers
	controller.NewUserController(authService, userService, trafficService).Register(engine)
	controller.NewAdminController(authService, userService, trafficService, proxyService).Register(engine)
	controller.NewPluginController(authService, trafficService).Register(engine)
	controller.NewRBACController().Register(engine)

	return nil
}
