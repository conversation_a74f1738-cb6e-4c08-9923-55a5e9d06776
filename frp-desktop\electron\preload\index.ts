import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('electronAPI', {
  // Window management
  openWin: (arg: string) => ipcRenderer.invoke('open-win', arg),
  
  // FRPC process management
  frpc: {
    start: (configPath: string) => ipcRenderer.invoke('frpc-start', configPath),
    stop: () => ipcRenderer.invoke('frpc-stop'),
    getStatus: () => ipcRenderer.invoke('frpc-status'),
    testBinary: () => ipcRenderer.invoke('frpc-test-binary'),
    
    // Event listeners
    onLog: (callback: (event: any, data: { type: string, data: string }) => void) => {
      ipcRenderer.on('frpc-log', callback)
    },
    onStatusChange: (callback: (event: any, data: { running: boolean, code?: number }) => void) => {
      ipcRenderer.on('frpc-status', callback)
    },
    
    // Remove listeners
    removeLogListener: (callback: any) => {
      ipc<PERSON>enderer.removeListener('frpc-log', callback)
    },
    removeStatusListener: (callback: any) => {
      ipcRenderer.removeListener('frpc-status', callback)
    }
  },
  
  // Config management
  config: {
    save: (configPath: string, content: string) => ipcRenderer.invoke('config-save', configPath, content),
    load: (configPath: string) => ipcRenderer.invoke('config-load', configPath),
    getDefaultPath: () => ipcRenderer.invoke('config-get-default-path'),
    loadStructured: () => ipcRenderer.invoke('config-load-structured'),
    saveStructured: (config: any) => ipcRenderer.invoke('config-save-structured', config)
  },
  
  // File dialogs
  dialog: {
    showOpenDialog: (options: any) => ipcRenderer.invoke('show-open-dialog', options),
    showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options)
  },

  // Log management
  logs: {
    getApp: (lines?: number) => ipcRenderer.invoke('logs-get-app', lines),
    getFrpc: (lines?: number) => ipcRenderer.invoke('logs-get-frpc', lines),
    clear: (type?: 'app' | 'frpc' | 'all') => ipcRenderer.invoke('logs-clear', type),
    getPaths: () => ipcRenderer.invoke('logs-get-paths')
  },
  
  // System info
  platform: process.platform,
  
  // Main process messages
  onMainProcessMessage: (callback: (event: any, message: string) => void) => {
    ipcRenderer.on('main-process-message', callback)
  },
  removeMainProcessMessageListener: (callback: any) => {
    ipcRenderer.removeListener('main-process-message', callback)
  }
})

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', {
      ipcRenderer: {
        send: (channel: string, data: any) => ipcRenderer.send(channel, data),
        invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
        on: (channel: string, func: (...args: any[]) => void) => {
          const subscription = (_event: any, ...args: any[]) => func(...args)
          ipcRenderer.on(channel, subscription)
          return () => ipcRenderer.removeListener(channel, subscription)
        },
        once: (channel: string, func: (...args: any[]) => void) => {
          ipcRenderer.once(channel, (_event, ...args) => func(...args))
        }
      }
    })
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = {
    ipcRenderer: {
      send: (channel: string, data: any) => ipcRenderer.send(channel, data),
      invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
      on: (channel: string, func: (...args: any[]) => void) => {
        const subscription = (_event: any, ...args: any[]) => func(...args)
        ipcRenderer.on(channel, subscription)
        return () => ipcRenderer.removeListener(channel, subscription)
      },
      once: (channel: string, func: (...args: any[]) => void) => {
        ipcRenderer.once(channel, (_event, ...args) => func(...args))
      }
    }
  }
}
