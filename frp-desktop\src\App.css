/* Global styles */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5715;
  color: #262626;
  background-color: #f0f2f5;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Ant Design customizations */
.ant-layout {
  background: #f0f2f5;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

.ant-card-head {
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  min-height: 56px;
}

.ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.ant-input,
.ant-input-affix-wrapper {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  font-size: 14px;
  padding: 8px 12px;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-focused,
.ant-input-affix-wrapper-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
