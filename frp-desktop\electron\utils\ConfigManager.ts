import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs'

export interface FrpcConfig {
  serverAddr: string
  serverPort: number
  token: string
  user: string
  metadatas?: Record<string, string>
  proxies: FrpcProxy[]
}

export interface FrpcProxy {
  name: string
  type: 'tcp' | 'udp' | 'http' | 'https' | 'stcp' | 'sudp' | 'xtcp' | 'tcpmux'
  localIP: string
  localPort: number
  remotePort?: number
  customDomains?: string[]
  subdomain?: string
  httpUser?: string
  httpPwd?: string
  useEncryption?: boolean
  useCompression?: boolean
  bandwidthLimit?: string
  [key: string]: any
}

export class ConfigManager {
  private configDir: string
  private configPath: string

  constructor() {
    // Use the existing conf directory in the project root
    if (!app.isPackaged) {
      // In development, use the conf directory from project root
      this.configDir = join(__dirname, '../../../conf')
      this.configPath = join(this.configDir, 'frpc.toml')
    } else {
      // In production, use user data directory
      this.configDir = join(app.getPath('userData'), 'configs')
      this.configPath = join(this.configDir, 'frpc.toml')
      this.ensureConfigDir()
    }
  }

  private ensureConfigDir() {
    // Only create directory in production mode
    if (app.isPackaged && !existsSync(this.configDir)) {
      mkdirSync(this.configDir, { recursive: true })
    }
  }

  getConfigPath(): string {
    return this.configPath
  }

  loadConfig(): FrpcConfig | null {
    try {
      if (!existsSync(this.configPath)) {
        return this.getDefaultConfig()
      }
      
      const content = readFileSync(this.configPath, 'utf8')
      return this.parseTomlConfig(content)
    } catch (error) {
      console.error('Failed to load config:', error)
      return null
    }
  }

  saveConfig(config: FrpcConfig): boolean {
    try {
      const tomlContent = this.generateTomlConfig(config)
      writeFileSync(this.configPath, tomlContent, 'utf8')
      return true
    } catch (error) {
      console.error('Failed to save config:', error)
      return false
    }
  }

  private getDefaultConfig(): FrpcConfig {
    return {
      serverAddr: '127.0.0.1',
      serverPort: 7000,
      token: '',
      user: '',
      metadatas: {},
      proxies: []
    }
  }

  private parseTomlConfig(content: string): FrpcConfig {
    // Parse new TOML format for frpc config
    const config: FrpcConfig = {
      serverAddr: '127.0.0.1',
      serverPort: 7000,
      token: '',
      user: '',
      metadatas: {},
      proxies: []
    }

    const lines = content.split('\n')
    let currentProxy: Partial<FrpcProxy> | null = null
    let inProxySection = false

    for (const line of lines) {
      const trimmed = line.trim()
      if (!trimmed || trimmed.startsWith('#')) continue

      // Handle [[proxies]] sections
      if (trimmed === '[[proxies]]') {
        // Save previous proxy if exists
        if (currentProxy && currentProxy.name) {
          config.proxies.push(currentProxy as FrpcProxy)
        }
        currentProxy = {}
        inProxySection = true
        continue
      }

      // Handle other sections
      if (trimmed.startsWith('[') && trimmed.endsWith(']') && trimmed !== '[[proxies]]') {
        // Save current proxy if we were in proxy section
        if (inProxySection && currentProxy && currentProxy.name) {
          config.proxies.push(currentProxy as FrpcProxy)
          currentProxy = null
        }
        inProxySection = false
        continue
      }

      // Key-value pairs
      const equalIndex = trimmed.indexOf('=')
      if (equalIndex === -1) continue

      const key = trimmed.slice(0, equalIndex).trim()
      let value = trimmed.slice(equalIndex + 1).trim()

      // Remove quotes and parse arrays
      if (value.startsWith('"') && value.endsWith('"')) {
        value = value.slice(1, -1)
      } else if (value.startsWith('[') && value.endsWith(']')) {
        // Parse array
        const arrayContent = value.slice(1, -1)
        const arrayValues = arrayContent.split(',').map(v => v.trim().replace(/"/g, ''))

        if (inProxySection && currentProxy) {
          if (key === 'customDomains') {
            currentProxy.customDomains = arrayValues
          }
        }
        continue
      }

      if (inProxySection && currentProxy) {
        // Proxy configuration
        switch (key) {
          case 'name':
            currentProxy.name = value
            break
          case 'type':
            currentProxy.type = value as any
            break
          case 'localIP':
            currentProxy.localIP = value
            break
          case 'localPort':
            currentProxy.localPort = parseInt(value)
            break
          case 'remotePort':
            currentProxy.remotePort = parseInt(value)
            break
          case 'subdomain':
            currentProxy.subdomain = value
            break
          case 'httpUser':
            currentProxy.httpUser = value
            break
          case 'httpPwd':
            currentProxy.httpPwd = value
            break
          case 'useEncryption':
            currentProxy.useEncryption = value === 'true'
            break
          case 'useCompression':
            currentProxy.useCompression = value === 'true'
            break
          case 'bandwidthLimit':
            currentProxy.bandwidthLimit = value
            break
          default:
            if (key.startsWith('metadatas.')) {
              // Skip metadata for now
            } else {
              currentProxy[key] = value
            }
        }
      } else {
        // Global configuration
        switch (key) {
          case 'serverAddr':
            config.serverAddr = value
            break
          case 'serverPort':
            config.serverPort = parseInt(value)
            break
          default:
            if (key.startsWith('metadatas.')) {
              const metaKey = key.slice(10) // Remove 'metadatas.'
              if (!config.metadatas) config.metadatas = {}
              config.metadatas[metaKey] = value

              // Also set token and user for backward compatibility
              if (metaKey === 'token') {
                config.token = value
              } else if (metaKey === 'username') {
                config.user = value
              }
            }
        }
      }
    }

    // Save last proxy
    if (inProxySection && currentProxy && currentProxy.name) {
      config.proxies.push(currentProxy as FrpcProxy)
    }

    return config
  }

  private generateTomlConfig(config: FrpcConfig): string {
    let content = ''

    // Global configuration
    content += `serverAddr = "${config.serverAddr}"\n`
    content += `serverPort = ${config.serverPort}\n`

    // Add metadata
    if (config.metadatas) {
      for (const [key, value] of Object.entries(config.metadatas)) {
        content += `metadatas.${key} = "${value}"\n`
      }
    } else {
      // Fallback to token and user
      if (config.token) {
        content += `metadatas.token = "${config.token}"\n`
      }
      if (config.user) {
        content += `metadatas.username = "${config.user}"\n`
      }
    }

    content += '\n'

    // Add proxies using new format
    for (const proxy of config.proxies) {
      content += `[[proxies]]\n`
      content += `name = "${proxy.name}"\n`
      content += `type = "${proxy.type}"\n`
      content += `localIP = "${proxy.localIP}"\n`
      content += `localPort = ${proxy.localPort}\n`

      if (proxy.remotePort) {
        content += `remotePort = ${proxy.remotePort}\n`
      }

      if (proxy.customDomains && proxy.customDomains.length > 0) {
        const domains = proxy.customDomains.map(d => `"${d}"`).join(', ')
        content += `customDomains = [${domains}]\n`
      }

      if (proxy.subdomain) {
        content += `subdomain = "${proxy.subdomain}"\n`
      }

      if (proxy.httpUser) {
        content += `httpUser = "${proxy.httpUser}"\n`
      }

      if (proxy.httpPwd) {
        content += `httpPwd = "${proxy.httpPwd}"\n`
      }

      if (proxy.useEncryption !== undefined) {
        content += `useEncryption = ${proxy.useEncryption}\n`
      }

      if (proxy.useCompression !== undefined) {
        content += `useCompression = ${proxy.useCompression}\n`
      }

      if (proxy.bandwidthLimit) {
        content += `bandwidthLimit = "${proxy.bandwidthLimit}"\n`
      }

      // Add metadata for each proxy
      if (config.metadatas) {
        for (const [key, value] of Object.entries(config.metadatas)) {
          content += `metadatas.${key} = "${value}"\n`
        }
      }

      content += '\n'
    }

    return content
  }
}
