package database

import (
	"github.com/glebarez/sqlite"
	"github.com/gofrp/fp-multiuser/pkg/model"
	"gorm.io/gorm"
)

var DB *gorm.DB

func Init(dsn string) error {
	var err error
	DB, err = gorm.Open(sqlite.Open(dsn), &gorm.Config{})
	if err != nil {
		return err
	}

	// 自动迁移所有模型
	return DB.AutoMigrate(
		&model.User{},
		&model.Service{},
		&model.TrafficUsage{},
		&model.Proxy{},
		&model.RBACAudit{},
		&model.ServiceSubscription{},
		&model.FrpsNode{},
		&model.FrpsNodeStats{},
	)
}
