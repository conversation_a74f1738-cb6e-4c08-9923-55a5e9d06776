import React, { useEffect, useState } from 'react'
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Badge,
  message,
  Alert,
  Divider
} from 'antd'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { startFrpc, stopFrpc, getFrpcStatus } from '../store/slices/frpcSlice'
import { loadConfig } from '../store/slices/configSlice'
import ServiceMonitor from '../components/Service/ServiceMonitor'
import UserStats from '../components/User/UserStats'
import './Dashboard.css'

const { Title, Text, Paragraph } = Typography

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { user } = useAppSelector(state => state.auth)
  const { isRunning, loading, error, logs } = useAppSelector(state => state.frpc)
  const { config, configPath } = useAppSelector(state => state.config)
  const [lastLogTime, setLastLogTime] = useState<string>('')

  useEffect(() => {
    // Load config and check FRPC status on component mount
    dispatch(loadConfig())
    dispatch(getFrpcStatus())
  }, [dispatch])

  useEffect(() => {
    // Update last log time when new logs arrive
    if (logs.length > 0) {
      const lastLog = logs[logs.length - 1]
      setLastLogTime(new Date(lastLog.timestamp).toLocaleString())
    }
  }, [logs])

  const handleStartStop = async () => {
    if (isRunning) {
      try {
        await dispatch(stopFrpc()).unwrap()
        message.success('FRPC 服务已停止')
      } catch (error: any) {
        message.error(`停止失败: ${error}`)
      }
    } else {
      if (!configPath) {
        message.error('配置文件路径未找到，请先配置')
        navigate('/config')
        return
      }

      if (config.proxies.length === 0) {
        message.warning('未配置任何隧道，请先添加隧道配置')
        navigate('/config')
        return
      }

      try {
        await dispatch(startFrpc(configPath)).unwrap()
        message.success('FRPC 服务已启动')
      } catch (error: any) {
        message.error(`启动失败: ${error}`)
      }
    }
  }

  const handleRefreshStatus = () => {
    dispatch(getFrpcStatus())
  }

  const handleTestBinary = async () => {
    try {
      const result = await window.electronAPI.frpc.testBinary()
      if (result.success) {
        message.success(`FRPC 二进制文件测试成功: ${result.path}`)
      } else {
        message.error(`FRPC 二进制文件测试失败: ${result.message}`)
      }
    } catch (error: any) {
      message.error(`测试失败: ${error.message}`)
    }
  }

  const getStatusColor = () => {
    if (loading) return 'processing'
    return isRunning ? 'success' : 'default'
  }

  const getStatusText = () => {
    if (loading) return '检查中...'
    return isRunning ? '运行中' : '已停止'
  }

  const recentLogs = logs.slice(-5).reverse()

  return (
    <div className="dashboard">
      <Row gutter={[24, 24]}>
        {/* Main Control Card */}
        <Col span={24}>
          <Card className="control-card">
            <div className="control-header">
              <Space align="center">
                <Badge status={getStatusColor()} />
                <Title level={3} style={{ margin: 0 }}>
                  FRPC 服务控制
                </Title>
                <Text type="secondary">({getStatusText()})</Text>
              </Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefreshStatus}
                loading={loading}
              >
                刷新状态
              </Button>
              <Button
                onClick={handleTestBinary}
              >
                测试二进制
              </Button>
            </div>

            <Divider />

            <div className="control-content">
              <Row gutter={[32, 32]} align="middle">
                <Col xs={24} lg={14}>
                  <div className="control-button-container">
                    <Button
                      type="primary"
                      size="large"
                      icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                      onClick={handleStartStop}
                      loading={loading}
                      className={`control-button ${isRunning ? 'stop-button' : 'start-button'}`}
                    >
                      {isRunning ? '停止 FRPC 服务' : '启动 FRPC 服务'}
                    </Button>
                  </div>
                </Col>

                <Col xs={24} lg={10}>
                  <Space direction="vertical" size="large" style={{ width: '100%' }}>
                    <Button
                      size="large"
                      icon={<SettingOutlined />}
                      onClick={() => navigate('/config')}
                      block
                      style={{ height: '48px', fontSize: '16px' }}
                    >
                      配置管理
                    </Button>
                    <Button
                      size="large"
                      icon={<InfoCircleOutlined />}
                      onClick={() => navigate('/logs')}
                      block
                      style={{ height: '48px', fontSize: '16px' }}
                    >
                      查看日志
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            {error && (
              <Alert
                message="错误"
                description={error}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
        </Col>

        {/* Service Monitor */}
        <Col span={24}>
          <ServiceMonitor />
        </Col>

        {/* User Stats Card */}
        <Col xs={24} lg={12}>
          <UserStats />
        </Col>

        {/* Recent Logs Card */}
        <Col xs={24} lg={12}>
          <Card 
            title="最近日志" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/logs')}
                size="small"
              >
                查看全部
              </Button>
            }
          >
            {recentLogs.length > 0 ? (
              <div className="recent-logs">
                {recentLogs.map((log, index) => (
                  <div key={index} className="log-item">
                    <Text 
                      type="secondary" 
                      style={{ fontSize: '12px' }}
                    >
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </Text>
                    <Paragraph 
                      ellipsis={{ rows: 1, tooltip: log.message }}
                      style={{ margin: 0, fontSize: '13px' }}
                    >
                      {log.message}
                    </Paragraph>
                  </div>
                ))}
              </div>
            ) : (
              <Text type="secondary">暂无日志</Text>
            )}
          </Card>
        </Col>

        {/* Server Config Info */}
        <Col span={24}>
          <Card title="服务器配置">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <Text type="secondary">服务器地址</Text>
                  <div>
                    <Text strong>{config.serverAddr}</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <Text type="secondary">服务器端口</Text>
                  <div>
                    <Text strong>{config.serverPort}</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <Text type="secondary">用户标识</Text>
                  <div>
                    <Text strong>{config.user || '未设置'}</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div>
                  <Text type="secondary">认证令牌</Text>
                  <div>
                    <Text strong>{config.token ? '已设置' : '未设置'}</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
