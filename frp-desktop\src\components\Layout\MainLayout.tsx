import React, { useEffect } from "react";
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Typo<PERSON>,
  Button,
} from "antd";
import {
  DashboardOutlined,
  SettingOutlined,
  FileTextOutlined,
  UserOutlined,
  LogoutOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { useNavigate, useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { logout } from "../../store/slices/authSlice";
import { addLog, setStatus } from "../../store/slices/frpcSlice";
import "./MainLayout.css";

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // Set up FRPC event listeners
    const handleFrpcLog = (
      event: any,
      data: { type: string; data: string }
    ) => {
      dispatch(addLog(data));
    };

    const handleFrpcStatus = (
      event: any,
      data: { running: boolean; code?: number }
    ) => {
      dispatch(setStatus(data));
    };

    // Add event listeners
    window.electronAPI.frpc.onLog(handleFrpcLog);
    window.electronAPI.frpc.onStatusChange(handleFrpcStatus);

    // Cleanup
    return () => {
      window.electronAPI.frpc.removeLogListener(handleFrpcLog);
      window.electronAPI.frpc.removeStatusListener(handleFrpcStatus);
    };
  }, [dispatch]);

  const handleLogout = () => {
    dispatch(logout());
    navigate("/login");
  };

  const menuItems = [
    {
      key: "/",
      icon: <DashboardOutlined />,
      label: "控制台",
    },
    {
      key: "/config",
      icon: <SettingOutlined />,
      label: "配置管理",
    },
    {
      key: "/logs",
      icon: <FileTextOutlined />,
      label: "日志查看",
    },
  ];

  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "个人信息",
    },
    {
      key: "about",
      icon: <InfoCircleOutlined />,
      label: "关于",
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "退出登录",
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout className="main-layout">
      <Sider theme="dark" width={260} className="main-sider">
        <div className="logo">
          <Text strong style={{ fontSize: "18px" }}>
            FRP Desktop
          </Text>
        </div>

        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="main-menu"
        />
      </Sider>

      <Layout style={{ width: "100%" }}>
        <Header className="main-header">
          <div className="header-content">
            <div className="header-title">
              <Text strong style={{ fontSize: "16px" }}>
                {menuItems.find((item) => item.key === location.pathname)
                  ?.label || "控制台"}
              </Text>
            </div>

            <div className="header-actions">
              <Space>
                <Dropdown
                  menu={{ items: userMenuItems }}
                  placement="bottomRight"
                  trigger={["click"]}
                >
                  <Button type="text" className="user-button">
                    <Space>
                      <Avatar size="small" icon={<UserOutlined />} />
                      <Text>{user?.username || "User"}</Text>
                    </Space>
                  </Button>
                </Dropdown>
              </Space>
            </div>
          </div>
        </Header>

        <Content className="main-content">{children}</Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
