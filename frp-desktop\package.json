{"name": "frp-desktop", "version": "1.0.0", "main": "dist-electron/index.js", "description": "FRP客户端桌面应用，基于Electron + React + Vite构建", "author": "FRP Desktop Team", "license": "MIT", "private": true, "keywords": ["frp", "frpc", "proxy", "electron-app", "react", "vite"], "engines": {"node": ">= 18"}, "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:3344/"}}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:electron": "npm run build && electron-builder --mac --win --linux", "build:electron:win": "npm run build && electron-builder --win", "build:electron:mac": "npm run build && electron-builder --mac", "build:electron:linux": "npm run build && electron-builder --linux", "lint": "eslint .", "preview": "vite preview", "electron:dev": "concurrently \"vite\" \"wait-on http://localhost:3344 && electron .\"", "electron:pack": "npm run build && electron-builder", "postinstall": "electron-builder install-app-deps", "test": "node scripts/test.js", "start": "node scripts/start.js", "build:script": "node scripts/build.js", "check-frpc": "node check-frpc.js", "test-config": "node test-config-parse.js"}, "dependencies": {"axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "lodash": "^4.17.21"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@vitejs/plugin-react": "^4.2.1", "electron": "^28.2.0", "electron-builder": "^24.13.3", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "typescript": "^5.2.2", "vite": "^5.1.4"}}