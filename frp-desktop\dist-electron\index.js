"use strict";
const electron = require("electron");
const path = require("path");
const child_process = require("child_process");
const fs = require("fs");
class ConfigManager {
  constructor() {
    if (!electron.app.isPackaged) {
      this.configDir = path.join(__dirname, "../../../conf");
      this.configPath = path.join(this.configDir, "frpc.toml");
    } else {
      this.configDir = path.join(electron.app.getPath("userData"), "configs");
      this.configPath = path.join(this.configDir, "frpc.toml");
      this.ensureConfigDir();
    }
  }
  ensureConfigDir() {
    if (electron.app.isPackaged && !fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
  }
  getConfigPath() {
    return this.configPath;
  }
  loadConfig() {
    try {
      if (!fs.existsSync(this.configPath)) {
        return this.getDefaultConfig();
      }
      const content = fs.readFileSync(this.configPath, "utf8");
      return this.parseTomlConfig(content);
    } catch (error) {
      console.error("Failed to load config:", error);
      return null;
    }
  }
  saveConfig(config) {
    try {
      const tomlContent = this.generateTomlConfig(config);
      fs.writeFileSync(this.configPath, tomlContent, "utf8");
      return true;
    } catch (error) {
      console.error("Failed to save config:", error);
      return false;
    }
  }
  getDefaultConfig() {
    return {
      serverAddr: "127.0.0.1",
      serverPort: 7e3,
      token: "",
      user: "",
      metadatas: {},
      proxies: []
    };
  }
  parseTomlConfig(content) {
    const config = {
      serverAddr: "127.0.0.1",
      serverPort: 7e3,
      token: "",
      user: "",
      metadatas: {},
      proxies: []
    };
    const lines = content.split("\n");
    let currentProxy = null;
    let inProxySection = false;
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith("#")) continue;
      if (trimmed === "[[proxies]]") {
        if (currentProxy && currentProxy.name) {
          config.proxies.push(currentProxy);
        }
        currentProxy = {};
        inProxySection = true;
        continue;
      }
      if (trimmed.startsWith("[") && trimmed.endsWith("]") && trimmed !== "[[proxies]]") {
        if (inProxySection && currentProxy && currentProxy.name) {
          config.proxies.push(currentProxy);
          currentProxy = null;
        }
        inProxySection = false;
        continue;
      }
      const equalIndex = trimmed.indexOf("=");
      if (equalIndex === -1) continue;
      const key = trimmed.slice(0, equalIndex).trim();
      let value = trimmed.slice(equalIndex + 1).trim();
      if (value.startsWith('"') && value.endsWith('"')) {
        value = value.slice(1, -1);
      } else if (value.startsWith("[") && value.endsWith("]")) {
        const arrayContent = value.slice(1, -1);
        const arrayValues = arrayContent.split(",").map((v) => v.trim().replace(/"/g, ""));
        if (inProxySection && currentProxy) {
          if (key === "customDomains") {
            currentProxy.customDomains = arrayValues;
          }
        }
        continue;
      }
      if (inProxySection && currentProxy) {
        switch (key) {
          case "name":
            currentProxy.name = value;
            break;
          case "type":
            currentProxy.type = value;
            break;
          case "localIP":
            currentProxy.localIP = value;
            break;
          case "localPort":
            currentProxy.localPort = parseInt(value);
            break;
          case "remotePort":
            currentProxy.remotePort = parseInt(value);
            break;
          case "subdomain":
            currentProxy.subdomain = value;
            break;
          case "httpUser":
            currentProxy.httpUser = value;
            break;
          case "httpPwd":
            currentProxy.httpPwd = value;
            break;
          case "useEncryption":
            currentProxy.useEncryption = value === "true";
            break;
          case "useCompression":
            currentProxy.useCompression = value === "true";
            break;
          case "bandwidthLimit":
            currentProxy.bandwidthLimit = value;
            break;
          default:
            if (key.startsWith("metadatas.")) ;
            else {
              currentProxy[key] = value;
            }
        }
      } else {
        switch (key) {
          case "serverAddr":
            config.serverAddr = value;
            break;
          case "serverPort":
            config.serverPort = parseInt(value);
            break;
          default:
            if (key.startsWith("metadatas.")) {
              const metaKey = key.slice(10);
              if (!config.metadatas) config.metadatas = {};
              config.metadatas[metaKey] = value;
              if (metaKey === "token") {
                config.token = value;
              } else if (metaKey === "username") {
                config.user = value;
              }
            }
        }
      }
    }
    if (inProxySection && currentProxy && currentProxy.name) {
      config.proxies.push(currentProxy);
    }
    return config;
  }
  generateTomlConfig(config) {
    let content = "";
    content += `serverAddr = "${config.serverAddr}"
`;
    content += `serverPort = ${config.serverPort}
`;
    if (config.metadatas) {
      for (const [key, value] of Object.entries(config.metadatas)) {
        content += `metadatas.${key} = "${value}"
`;
      }
    } else {
      if (config.token) {
        content += `metadatas.token = "${config.token}"
`;
      }
      if (config.user) {
        content += `metadatas.username = "${config.user}"
`;
      }
    }
    content += "\n";
    for (const proxy of config.proxies) {
      content += `[[proxies]]
`;
      content += `name = "${proxy.name}"
`;
      content += `type = "${proxy.type}"
`;
      content += `localIP = "${proxy.localIP}"
`;
      content += `localPort = ${proxy.localPort}
`;
      if (proxy.remotePort) {
        content += `remotePort = ${proxy.remotePort}
`;
      }
      if (proxy.customDomains && proxy.customDomains.length > 0) {
        const domains = proxy.customDomains.map((d) => `"${d}"`).join(", ");
        content += `customDomains = [${domains}]
`;
      }
      if (proxy.subdomain) {
        content += `subdomain = "${proxy.subdomain}"
`;
      }
      if (proxy.httpUser) {
        content += `httpUser = "${proxy.httpUser}"
`;
      }
      if (proxy.httpPwd) {
        content += `httpPwd = "${proxy.httpPwd}"
`;
      }
      if (proxy.useEncryption !== void 0) {
        content += `useEncryption = ${proxy.useEncryption}
`;
      }
      if (proxy.useCompression !== void 0) {
        content += `useCompression = ${proxy.useCompression}
`;
      }
      if (proxy.bandwidthLimit) {
        content += `bandwidthLimit = "${proxy.bandwidthLimit}"
`;
      }
      if (config.metadatas) {
        for (const [key, value] of Object.entries(config.metadatas)) {
          content += `metadatas.${key} = "${value}"
`;
        }
      }
      content += "\n";
    }
    return content;
  }
}
class LogManager {
  constructor() {
    this.maxLogSize = 10 * 1024 * 1024;
    this.maxLogFiles = 5;
    this.logDir = path.join(electron.app.getPath("userData"), "logs");
    this.appLogPath = path.join(this.logDir, "app.log");
    this.frpcLogPath = path.join(this.logDir, "frpc.log");
    this.ensureLogDir();
  }
  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }
  rotateLogFile(logPath) {
    try {
      if (!fs.existsSync(logPath)) return;
      const stats = require("fs").statSync(logPath);
      if (stats.size < this.maxLogSize) return;
      for (let i = this.maxLogFiles - 1; i > 0; i--) {
        const oldPath = `${logPath}.${i}`;
        const newPath = `${logPath}.${i + 1}`;
        if (fs.existsSync(oldPath)) {
          if (i === this.maxLogFiles - 1) {
            require("fs").unlinkSync(oldPath);
          } else {
            require("fs").renameSync(oldPath, newPath);
          }
        }
      }
      require("fs").renameSync(logPath, `${logPath}.1`);
    } catch (error) {
      console.error("Failed to rotate log file:", error);
    }
  }
  writeLog(logPath, entry) {
    try {
      this.rotateLogFile(logPath);
      const logLine = `[${entry.timestamp}] [${entry.level.toUpperCase()}] [${entry.source}] ${entry.message}
`;
      fs.appendFileSync(logPath, logLine, "utf8");
    } catch (error) {
      console.error("Failed to write log:", error);
    }
  }
  logApp(level, message) {
    const entry = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      level,
      source: "app",
      message
    };
    this.writeLog(this.appLogPath, entry);
    if (!electron.app.isPackaged) {
      console.log(`[${entry.level.toUpperCase()}] ${message}`);
    }
  }
  logFrpc(message, type = "stdout") {
    const level = type === "stderr" || type === "error" ? "error" : "info";
    const entry = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      level,
      source: "frpc",
      message: message.trim()
    };
    this.writeLog(this.frpcLogPath, entry);
  }
  getAppLogs(lines = 100) {
    return this.readLogFile(this.appLogPath, lines);
  }
  getFrpcLogs(lines = 100) {
    return this.readLogFile(this.frpcLogPath, lines);
  }
  readLogFile(logPath, lines) {
    try {
      if (!fs.existsSync(logPath)) return [];
      const content = fs.readFileSync(logPath, "utf8");
      const logLines = content.split("\n").filter((line) => line.trim());
      const recentLines = logLines.slice(-lines);
      return recentLines.map((line) => this.parseLogLine(line)).filter((entry) => entry !== null);
    } catch (error) {
      console.error("Failed to read log file:", error);
      return [];
    }
  }
  parseLogLine(line) {
    try {
      const match = line.match(/^\[([^\]]+)\] \[([^\]]+)\] \[([^\]]+)\] (.+)$/);
      if (!match) return null;
      const [, timestamp, level, source, message] = match;
      return {
        timestamp,
        level: level.toLowerCase(),
        source,
        message
      };
    } catch (error) {
      return null;
    }
  }
  clearLogs(type = "all") {
    try {
      if (type === "app" || type === "all") {
        if (fs.existsSync(this.appLogPath)) {
          fs.writeFileSync(this.appLogPath, "", "utf8");
        }
      }
      if (type === "frpc" || type === "all") {
        if (fs.existsSync(this.frpcLogPath)) {
          fs.writeFileSync(this.frpcLogPath, "", "utf8");
        }
      }
      return true;
    } catch (error) {
      console.error("Failed to clear logs:", error);
      return false;
    }
  }
  getLogPaths() {
    return {
      app: this.appLogPath,
      frpc: this.frpcLogPath,
      dir: this.logDir
    };
  }
}
process.env.DIST = path.join(__dirname, "../..");
process.env.VITE_PUBLIC = electron.app.isPackaged ? process.env.DIST : path.join(process.env.DIST, "../public");
let win = null;
let frpcProcess = null;
let configManager;
let logManager;
const preload = path.join(__dirname, "./preload/index.js");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = path.join(process.env.DIST, "index.html");
async function createWindow() {
  win = new electron.BrowserWindow({
    title: "FRP Desktop Client",
    icon: process.env.VITE_PUBLIC ? path.join(process.env.VITE_PUBLIC, "favicon.ico") : void 0,
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  if (url) {
    win.loadURL(url);
    win.webContents.openDevTools();
  } else {
    win.loadFile(indexHtml);
  }
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.setWindowOpenHandler(({ url: url2 }) => {
    if (url2.startsWith("https:")) electron.shell.openExternal(url2);
    return { action: "deny" };
  });
}
electron.app.whenReady().then(() => {
  configManager = new ConfigManager();
  logManager = new LogManager();
  logManager.logApp("info", "Application starting...");
  createWindow();
});
electron.app.on("window-all-closed", () => {
  win = null;
  if (frpcProcess) {
    frpcProcess.kill();
    frpcProcess = null;
  }
  if (process.platform !== "darwin") electron.app.quit();
});
electron.app.on("second-instance", () => {
  if (win) {
    if (win.isMinimized()) win.restore();
    win.focus();
  }
});
electron.app.on("activate", () => {
  const allWindows = electron.BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createWindow();
  }
});
electron.ipcMain.handle("open-win", (_, arg) => {
  const childWindow = new electron.BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`);
  } else {
    childWindow.loadFile(indexHtml, { hash: arg });
  }
});
electron.ipcMain.handle("frpc-start", async (_, configPath) => {
  var _a, _b;
  try {
    if (frpcProcess) {
      logManager.logApp("warn", "FRPC is already running");
      return { success: false, message: "FRPC is already running" };
    }
    const frpcBinPath = getFrpcBinaryPath();
    logManager.logApp("info", `Trying to start FRPC with binary: ${frpcBinPath}`);
    if (!fs.existsSync(frpcBinPath)) {
      logManager.logApp("error", `FRPC binary not found at: ${frpcBinPath}`);
      return { success: false, message: `FRPC binary not found at: ${frpcBinPath}` };
    }
    if (!fs.existsSync(configPath)) {
      logManager.logApp("error", `Config file not found at: ${configPath}`);
      return { success: false, message: `Config file not found at: ${configPath}` };
    }
    logManager.logApp("info", `Starting FRPC with config: ${configPath}`);
    frpcProcess = child_process.spawn(frpcBinPath, ["-c", configPath], {
      stdio: ["pipe", "pipe", "pipe"],
      detached: false,
      shell: false
    });
    (_a = frpcProcess.stdout) == null ? void 0 : _a.on("data", (data) => {
      const message = data.toString();
      logManager.logFrpc(message, "stdout");
      win == null ? void 0 : win.webContents.send("frpc-log", { type: "stdout", data: message });
    });
    (_b = frpcProcess.stderr) == null ? void 0 : _b.on("data", (data) => {
      const message = data.toString();
      logManager.logFrpc(message, "stderr");
      win == null ? void 0 : win.webContents.send("frpc-log", { type: "stderr", data: message });
    });
    frpcProcess.on("close", (code) => {
      logManager.logApp("info", `FRPC process closed with code: ${code}`);
      win == null ? void 0 : win.webContents.send("frpc-status", { running: false, code });
      frpcProcess = null;
    });
    frpcProcess.on("error", (error) => {
      const message = error.message;
      logManager.logFrpc(message, "error");
      logManager.logApp("error", `FRPC process error: ${message}`);
      win == null ? void 0 : win.webContents.send("frpc-log", { type: "error", data: message });
      frpcProcess = null;
    });
    return { success: true, message: "FRPC started successfully" };
  } catch (error) {
    return { success: false, message: `Failed to start FRPC: ${error}` };
  }
});
electron.ipcMain.handle("frpc-stop", async () => {
  try {
    if (!frpcProcess) {
      logManager.logApp("warn", "FRPC is not running");
      return { success: false, message: "FRPC is not running" };
    }
    logManager.logApp("info", `Stopping FRPC process (PID: ${frpcProcess.pid})`);
    frpcProcess.kill("SIGTERM");
    setTimeout(() => {
      if (frpcProcess && !frpcProcess.killed) {
        logManager.logApp("warn", "Force killing FRPC process");
        frpcProcess.kill("SIGKILL");
      }
    }, 3e3);
    frpcProcess = null;
    logManager.logApp("info", "FRPC process stopped");
    return { success: true, message: "FRPC stopped successfully" };
  } catch (error) {
    logManager.logApp("error", `Failed to stop FRPC: ${error}`);
    return { success: false, message: `Failed to stop FRPC: ${error}` };
  }
});
electron.ipcMain.handle("frpc-status", async () => {
  return { running: frpcProcess !== null };
});
electron.ipcMain.handle("frpc-test-binary", async () => {
  try {
    const frpcBinPath = getFrpcBinaryPath();
    const exists = fs.existsSync(frpcBinPath);
    logManager.logApp("info", `Testing FRPC binary at: ${frpcBinPath}, exists: ${exists}`);
    if (!exists) {
      return {
        success: false,
        message: `FRPC binary not found at: ${frpcBinPath}`,
        path: frpcBinPath
      };
    }
    const testProcess = child_process.spawn(frpcBinPath, ["--help"], {
      stdio: ["pipe", "pipe", "pipe"],
      timeout: 5e3
    });
    return new Promise((resolve) => {
      var _a;
      let output = "";
      (_a = testProcess.stdout) == null ? void 0 : _a.on("data", (data) => {
        output += data.toString();
      });
      testProcess.on("close", (code) => {
        if (code === 0 || output.includes("frpc")) {
          logManager.logApp("info", "FRPC binary test successful");
          resolve({
            success: true,
            message: "FRPC binary is working",
            path: frpcBinPath,
            output: output.substring(0, 200)
          });
        } else {
          logManager.logApp("error", `FRPC binary test failed with code: ${code}`);
          resolve({
            success: false,
            message: `FRPC binary test failed with code: ${code}`,
            path: frpcBinPath
          });
        }
      });
      testProcess.on("error", (error) => {
        logManager.logApp("error", `FRPC binary test error: ${error.message}`);
        resolve({
          success: false,
          message: `FRPC binary test error: ${error.message}`,
          path: frpcBinPath
        });
      });
      setTimeout(() => {
        testProcess.kill();
        resolve({
          success: false,
          message: "FRPC binary test timeout",
          path: frpcBinPath
        });
      }, 5e3);
    });
  } catch (error) {
    logManager.logApp("error", `FRPC binary test exception: ${error}`);
    return {
      success: false,
      message: `FRPC binary test exception: ${error}`,
      path: getFrpcBinaryPath()
    };
  }
});
electron.ipcMain.handle("config-save", async (_, configPath, content) => {
  try {
    fs.writeFileSync(configPath, content, "utf8");
    return { success: true, message: "Config saved successfully" };
  } catch (error) {
    return { success: false, message: `Failed to save config: ${error}` };
  }
});
electron.ipcMain.handle("config-load", async (_, configPath) => {
  try {
    if (!fs.existsSync(configPath)) {
      return { success: false, message: "Config file not found" };
    }
    const content = fs.readFileSync(configPath, "utf8");
    return { success: true, content };
  } catch (error) {
    return { success: false, message: `Failed to load config: ${error}` };
  }
});
electron.ipcMain.handle("show-open-dialog", async (_, options) => {
  const result = await electron.dialog.showOpenDialog(win, options);
  return result;
});
electron.ipcMain.handle("show-save-dialog", async (_, options) => {
  const result = await electron.dialog.showSaveDialog(win, options);
  return result;
});
electron.ipcMain.handle("config-get-default-path", async () => {
  return configManager.getConfigPath();
});
electron.ipcMain.handle("config-load-structured", async () => {
  try {
    const config = configManager.loadConfig();
    return { success: true, config };
  } catch (error) {
    logManager.logApp("error", `Failed to load structured config: ${error}`);
    return { success: false, message: `Failed to load config: ${error}` };
  }
});
electron.ipcMain.handle("config-save-structured", async (_, config) => {
  try {
    const success = configManager.saveConfig(config);
    if (success) {
      logManager.logApp("info", "Configuration saved successfully");
      return { success: true, message: "Config saved successfully" };
    } else {
      return { success: false, message: "Failed to save config" };
    }
  } catch (error) {
    logManager.logApp("error", `Failed to save structured config: ${error}`);
    return { success: false, message: `Failed to save config: ${error}` };
  }
});
electron.ipcMain.handle("logs-get-app", async (_, lines = 100) => {
  try {
    const logs = logManager.getAppLogs(lines);
    return { success: true, logs };
  } catch (error) {
    return { success: false, message: `Failed to get app logs: ${error}` };
  }
});
electron.ipcMain.handle("logs-get-frpc", async (_, lines = 100) => {
  try {
    const logs = logManager.getFrpcLogs(lines);
    return { success: true, logs };
  } catch (error) {
    return { success: false, message: `Failed to get frpc logs: ${error}` };
  }
});
electron.ipcMain.handle("logs-clear", async (_, type = "all") => {
  try {
    const success = logManager.clearLogs(type);
    if (success) {
      logManager.logApp("info", `Cleared ${type} logs`);
      return { success: true, message: "Logs cleared successfully" };
    } else {
      return { success: false, message: "Failed to clear logs" };
    }
  } catch (error) {
    logManager.logApp("error", `Failed to clear logs: ${error}`);
    return { success: false, message: `Failed to clear logs: ${error}` };
  }
});
electron.ipcMain.handle("logs-get-paths", async () => {
  try {
    const paths = logManager.getLogPaths();
    return { success: true, paths };
  } catch (error) {
    return { success: false, message: `Failed to get log paths: ${error}` };
  }
});
function getFrpcBinaryPath() {
  const platform = process.platform;
  const isWindows = platform === "win32";
  if (!electron.app.isPackaged) {
    const basePaths = [
      path.join(__dirname, "../../../bin"),
      path.join(__dirname, "../../bin"),
      path.join(process.cwd(), "bin"),
      path.join(process.cwd(), "../bin")
    ];
    const binaryNames = isWindows ? ["frpc.exe", "frpc"] : ["frpc", "frpc.exe"];
    for (const basePath of basePaths) {
      for (const binaryName2 of binaryNames) {
        const fullPath = path.join(basePath, binaryName2);
        if (fs.existsSync(fullPath)) {
          logManager.logApp("info", `Found FRPC binary at: ${fullPath}`);
          return fullPath;
        }
      }
    }
    const defaultPath = path.join(__dirname, "../../../bin", "frpc");
    logManager.logApp("warn", `FRPC binary not found, using default path: ${defaultPath}`);
    return defaultPath;
  }
  const resourcesPath = process.resourcesPath;
  const binaryName = isWindows ? "frpc.exe" : "frpc";
  const binPath = path.join(resourcesPath, "bin", binaryName);
  return binPath;
}
