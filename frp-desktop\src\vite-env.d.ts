/// <reference types="vite/client" />

interface ElectronAPI {
  openWin: (arg: string) => Promise<void>
  frpc: {
    start: (configPath: string) => Promise<{ success: boolean; message: string }>
    stop: () => Promise<{ success: boolean; message: string }>
    getStatus: () => Promise<{ running: boolean }>
    testBinary: () => Promise<{ success: boolean; message: string; path: string; output?: string }>
    onLog: (callback: (event: any, data: { type: string; data: string }) => void) => void
    onStatusChange: (callback: (event: any, data: { running: boolean; code?: number }) => void) => void
    removeLogListener: (callback: any) => void
    removeStatusListener: (callback: any) => void
  }
  config: {
    save: (configPath: string, content: string) => Promise<{ success: boolean; message: string }>
    load: (configPath: string) => Promise<{ success: boolean; content?: string; message: string }>
    getDefaultPath: () => Promise<string>
    loadStructured: () => Promise<{ success: boolean; config?: any; message?: string }>
    saveStructured: (config: any) => Promise<{ success: boolean; message: string }>
  }
  dialog: {
    showOpenDialog: (options: any) => Promise<any>
    showSaveDialog: (options: any) => Promise<any>
  }
  logs: {
    getApp: (lines?: number) => Promise<{ success: boolean; logs?: any[]; message?: string }>
    getFrpc: (lines?: number) => Promise<{ success: boolean; logs?: any[]; message?: string }>
    clear: (type?: 'app' | 'frpc' | 'all') => Promise<{ success: boolean; message: string }>
    getPaths: () => Promise<{ success: boolean; paths?: any; message?: string }>
  }
  platform: string
  onMainProcessMessage: (callback: (event: any, message: string) => void) => void
  removeMainProcessMessageListener: (callback: any) => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    electron: {
      ipcRenderer: {
        send: (channel: string, data: any) => void
        invoke: (channel: string, ...args: any[]) => Promise<any>
        on: (channel: string, func: (...args: any[]) => void) => () => void
        once: (channel: string, func: (...args: any[]) => void) => void
      }
    }
  }
}
