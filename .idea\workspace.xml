<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="669af56e-8060-495c-a1de-742667dda4fc" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/frp-desktop/dist-electron/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/dist-electron/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frp-desktop/electron/main/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/electron/main/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frp-desktop/electron/preload/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/electron/preload/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frp-desktop/electron/utils/ConfigManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/electron/utils/ConfigManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frp-desktop/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frp-desktop/src/pages/Dashboard.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/src/pages/Dashboard.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frp-desktop/src/vite-env.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/frp-desktop/src/vite-env.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/users.db" beforeDir="false" afterPath="$PROJECT_DIR$/users.db" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/sdk/go1.24.3" />
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30OpPqFflJrpNPWfxlslwGDFgq7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;go.sdk.automatically.set&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Workspace/web/frp&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-33c477a475b1-e0158606a674-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-241.19072.18" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-GO-241.19072.18" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="javascript:npm:react" />
    <option featureType="dependencySupport" implementationName="javascript:npm:prettier" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vite" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vue" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>