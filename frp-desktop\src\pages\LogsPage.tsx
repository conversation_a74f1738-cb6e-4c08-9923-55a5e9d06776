import React, { useEffect, useState, useRef } from 'react'
import { 
  Card, 
  Tabs, 
  Button, 
  Space, 
  Typography, 
  Input, 
  Select, 
  message,
  Empty,
  Tag,
  Popconfirm
} from 'antd'
import { 
  ReloadOutlined, 
  ClearOutlined, 
  DownloadOutlined,
  SearchOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { loadAppLogs, loadFrpcLogs, clearLogs } from '../store/slices/logsSlice'
import { type LogEntry } from '../store/slices/logsSlice'
import './LogsPage.css'

const { TabPane } = Tabs
const { Text } = Typography
const { Search } = Input
const { Option } = Select

const LogsPage: React.FC = () => {
  const dispatch = useAppDispatch()
  const { appLogs, frpcLogs, loading } = useAppSelector(state => state.logs)
  const { logs: realtimeLogs } = useAppSelector(state => state.frpc)
  
  const [activeTab, setActiveTab] = useState('frpc')
  const [searchText, setSearchText] = useState('')
  const [logLevel, setLogLevel] = useState<string>('all')
  const [autoScroll, setAutoScroll] = useState(true)
  const [isPaused, setIsPaused] = useState(false)
  
  const logContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Load initial logs
    dispatch(loadAppLogs(500))
    dispatch(loadFrpcLogs(500))
  }, [dispatch])

  useEffect(() => {
    // Auto scroll to bottom when new logs arrive
    if (autoScroll && !isPaused && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [appLogs, frpcLogs, realtimeLogs, autoScroll, isPaused])

  const handleRefreshLogs = () => {
    if (activeTab === 'app') {
      dispatch(loadAppLogs(500))
    } else {
      dispatch(loadFrpcLogs(500))
    }
    message.success('日志已刷新')
  }

  const handleClearLogs = async (type: 'app' | 'frpc' | 'all') => {
    try {
      await dispatch(clearLogs(type)).unwrap()
      message.success('日志已清空')
    } catch (error: any) {
      message.error(`清空日志失败: ${error}`)
    }
  }

  const handleExportLogs = () => {
    const logs = activeTab === 'app' ? appLogs : 
                 activeTab === 'frpc' ? frpcLogs : realtimeLogs
    
    const logText = logs.map(log => 
      `[${log.timestamp}] [${log.level?.toUpperCase() || 'INFO'}] ${log.message}`
    ).join('\n')
    
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${activeTab}_logs_${new Date().toISOString().slice(0, 10)}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    message.success('日志已导出')
  }

  const filterLogs = (logs: LogEntry[]) => {
    return logs.filter(log => {
      const matchesSearch = !searchText || 
        log.message.toLowerCase().includes(searchText.toLowerCase())
      
      const matchesLevel = logLevel === 'all' || log.level === logLevel
      
      return matchesSearch && matchesLevel
    })
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  const getLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'error': return 'red'
      case 'warn': return 'orange'
      case 'info': return 'blue'
      case 'debug': return 'gray'
      default: return 'default'
    }
  }

  const renderLogList = (logs: LogEntry[]) => {
    const filteredLogs = filterLogs(logs)
    
    if (filteredLogs.length === 0) {
      return <Empty description="暂无日志" />
    }

    return (
      <div 
        className="log-list" 
        ref={logContainerRef}
        onScroll={(e) => {
          const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
          const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
          setAutoScroll(isAtBottom)
        }}
      >
        {filteredLogs.map((log, index) => (
          <div key={index} className="log-item">
            <div className="log-header">
              <Text type="secondary" className="log-timestamp">
                {formatTimestamp(log.timestamp)}
              </Text>
              <Tag color={getLevelColor(log.level)} className="log-level">
                {log.level?.toUpperCase() || 'INFO'}
              </Tag>
            </div>
            <div className="log-message">
              <Text>{log.message}</Text>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderRealtimeLogs = () => {
    const logs = realtimeLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.type === 'stderr' || log.type === 'error' ? 'error' as const : 'info' as const,
      source: 'frpc' as const,
      message: log.message
    }))
    
    return renderLogList(logs)
  }

  return (
    <div className="logs-page">
      <Card>
        <div className="logs-header">
          <Space wrap>
            <Search
              placeholder="搜索日志内容"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
            />
            
            <Select
              value={logLevel}
              onChange={setLogLevel}
              style={{ width: 120 }}
            >
              <Option value="all">全部级别</Option>
              <Option value="info">INFO</Option>
              <Option value="warn">WARN</Option>
              <Option value="error">ERROR</Option>
              <Option value="debug">DEBUG</Option>
            </Select>
            
            <Button
              icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
              onClick={() => setIsPaused(!isPaused)}
            >
              {isPaused ? '继续' : '暂停'}
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefreshLogs}
              loading={loading}
            >
              刷新
            </Button>
            
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportLogs}
            >
              导出
            </Button>
            
            <Popconfirm
              title="确定要清空日志吗？"
              onConfirm={() => handleClearLogs(activeTab as any)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<ClearOutlined />}
                danger
              >
                清空
              </Button>
            </Popconfirm>
          </Space>
        </div>

        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          className="logs-tabs"
        >
          <TabPane tab="FRPC 日志" key="frpc">
            {renderLogList(frpcLogs)}
          </TabPane>
          
          <TabPane tab="实时日志" key="realtime">
            {renderRealtimeLogs()}
          </TabPane>
          
          <TabPane tab="应用日志" key="app">
            {renderLogList(appLogs)}
          </TabPane>
        </Tabs>

        <div className="logs-footer">
          <Space>
            <Text type="secondary">
              自动滚动: {autoScroll ? '开启' : '关闭'}
            </Text>
            <Text type="secondary">
              暂停状态: {isPaused ? '已暂停' : '正常'}
            </Text>
            <Text type="secondary">
              显示条数: {
                activeTab === 'app' ? filterLogs(appLogs).length :
                activeTab === 'frpc' ? filterLogs(frpcLogs).length :
                filterLogs(realtimeLogs.map(log => ({
                  timestamp: log.timestamp,
                  level: log.type === 'stderr' || log.type === 'error' ? 'error' as const : 'info' as const,
                  source: 'frpc' as const,
                  message: log.message
                }))).length
              }
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default LogsPage
