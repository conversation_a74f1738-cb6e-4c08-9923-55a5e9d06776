.login-page {
  height: 100vh;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
}

.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-container {
  width: 100%;
  position: relative;
  z-index: 1;
}

.login-card {
  border-radius: 16px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
  padding-top: 8px;
}

.login-header .ant-typography h2 {
  margin-bottom: 8px;
  color: #262626;
  font-size: 28px;
  font-weight: 700;
}

.login-header .ant-typography-caption {
  color: #8c8c8c;
  font-size: 16px;
}

.login-tabs {
  margin-bottom: 8px;
}

.login-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 600;
  padding: 12px 24px;
}

.login-tabs .ant-tabs-tab-active {
  color: #1890ff;
}

.login-footer {
  margin-top: 32px;
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.ant-form-item-label > label {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.ant-btn-primary {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.ant-input-affix-wrapper,
.ant-input {
  height: 48px;
  border-radius: 12px;
  border: 2px solid #e8e8e8;
  font-size: 15px;
  padding: 0 16px;
}

.ant-input-affix-wrapper:focus,
.ant-input:focus,
.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.ant-card-body {
  padding: 40px;
}
