import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron'
import { join } from 'path'
import { spawn, ChildProcess } from 'child_process'
import { existsSync, readFileSync, writeFileSync } from 'fs'
import { ConfigManager } from '../utils/ConfigManager'
import { LogManager } from '../utils/LogManager'

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.js
// │
process.env.DIST = join(__dirname, '../..')
process.env.VITE_PUBLIC = app.isPackaged ? process.env.DIST : join(process.env.DIST, '../public')

let win: BrowserWindow | null = null
let frpcProcess: ChildProcess | null = null
let configManager: ConfigManager
let logManager: LogManager

// Here, you can also use other preload
const preload = join(__dirname, './preload/index.js')
const url = process.env.VITE_DEV_SERVER_URL
const indexHtml = join(process.env.DIST, 'index.html')

async function createWindow() {
  win = new BrowserWindow({
    title: 'FRP Desktop Client',
    icon: process.env.VITE_PUBLIC ? join(process.env.VITE_PUBLIC, 'favicon.ico') : undefined,
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
    },
  })

  if (url) { // electron-vite-vue#298
    win.loadURL(url)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    win.loadFile(indexHtml)
  }

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Initialize managers
  configManager = new ConfigManager()
  logManager = new LogManager()

  logManager.logApp('info', 'Application starting...')

  createWindow()
})

app.on('window-all-closed', () => {
  win = null
  // Stop frpc process when app is closing
  if (frpcProcess) {
    frpcProcess.kill()
    frpcProcess = null
  }
  if (process.platform !== 'darwin') app.quit()
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})

// New window example arg: new windows url
ipcMain.handle('open-win', (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
    },
  })

  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`)
  } else {
    childWindow.loadFile(indexHtml, { hash: arg })
  }
})

// FRPC Process Management
ipcMain.handle('frpc-start', async (_, configPath: string) => {
  try {
    if (frpcProcess) {
      logManager.logApp('warn', 'FRPC is already running')
      return { success: false, message: 'FRPC is already running' }
    }

    const frpcBinPath = getFrpcBinaryPath()
    logManager.logApp('info', `Trying to start FRPC with binary: ${frpcBinPath}`)

    if (!existsSync(frpcBinPath)) {
      logManager.logApp('error', `FRPC binary not found at: ${frpcBinPath}`)
      return { success: false, message: `FRPC binary not found at: ${frpcBinPath}` }
    }

    if (!existsSync(configPath)) {
      logManager.logApp('error', `Config file not found at: ${configPath}`)
      return { success: false, message: `Config file not found at: ${configPath}` }
    }

    logManager.logApp('info', `Starting FRPC with config: ${configPath}`)

    frpcProcess = spawn(frpcBinPath, ['-c', configPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      detached: false,
      shell: false
    })

    frpcProcess.stdout?.on('data', (data) => {
      const message = data.toString()
      logManager.logFrpc(message, 'stdout')
      win?.webContents.send('frpc-log', { type: 'stdout', data: message })
    })

    frpcProcess.stderr?.on('data', (data) => {
      const message = data.toString()
      logManager.logFrpc(message, 'stderr')
      win?.webContents.send('frpc-log', { type: 'stderr', data: message })
    })

    frpcProcess.on('close', (code) => {
      logManager.logApp('info', `FRPC process closed with code: ${code}`)
      win?.webContents.send('frpc-status', { running: false, code })
      frpcProcess = null
    })

    frpcProcess.on('error', (error) => {
      const message = error.message
      logManager.logFrpc(message, 'error')
      logManager.logApp('error', `FRPC process error: ${message}`)
      win?.webContents.send('frpc-log', { type: 'error', data: message })
      frpcProcess = null
    })

    return { success: true, message: 'FRPC started successfully' }
  } catch (error) {
    return { success: false, message: `Failed to start FRPC: ${error}` }
  }
})

ipcMain.handle('frpc-stop', async () => {
  try {
    if (!frpcProcess) {
      logManager.logApp('warn', 'FRPC is not running')
      return { success: false, message: 'FRPC is not running' }
    }

    logManager.logApp('info', `Stopping FRPC process (PID: ${frpcProcess.pid})`)

    // Try graceful shutdown first
    frpcProcess.kill('SIGTERM')

    // Wait a bit, then force kill if still running
    setTimeout(() => {
      if (frpcProcess && !frpcProcess.killed) {
        logManager.logApp('warn', 'Force killing FRPC process')
        frpcProcess.kill('SIGKILL')
      }
    }, 3000)

    frpcProcess = null
    logManager.logApp('info', 'FRPC process stopped')

    return { success: true, message: 'FRPC stopped successfully' }
  } catch (error) {
    logManager.logApp('error', `Failed to stop FRPC: ${error}`)
    return { success: false, message: `Failed to stop FRPC: ${error}` }
  }
})

ipcMain.handle('frpc-status', async () => {
  return { running: frpcProcess !== null }
})

// Test FRPC binary
ipcMain.handle('frpc-test-binary', async () => {
  try {
    const frpcBinPath = getFrpcBinaryPath()
    const exists = existsSync(frpcBinPath)

    logManager.logApp('info', `Testing FRPC binary at: ${frpcBinPath}, exists: ${exists}`)

    if (!exists) {
      return {
        success: false,
        message: `FRPC binary not found at: ${frpcBinPath}`,
        path: frpcBinPath
      }
    }

    // Try to run frpc --help to test if it's executable
    const testProcess = spawn(frpcBinPath, ['--help'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      timeout: 5000
    })

    return new Promise((resolve) => {
      let output = ''

      testProcess.stdout?.on('data', (data) => {
        output += data.toString()
      })

      testProcess.on('close', (code) => {
        if (code === 0 || output.includes('frpc')) {
          logManager.logApp('info', 'FRPC binary test successful')
          resolve({
            success: true,
            message: 'FRPC binary is working',
            path: frpcBinPath,
            output: output.substring(0, 200)
          })
        } else {
          logManager.logApp('error', `FRPC binary test failed with code: ${code}`)
          resolve({
            success: false,
            message: `FRPC binary test failed with code: ${code}`,
            path: frpcBinPath
          })
        }
      })

      testProcess.on('error', (error) => {
        logManager.logApp('error', `FRPC binary test error: ${error.message}`)
        resolve({
          success: false,
          message: `FRPC binary test error: ${error.message}`,
          path: frpcBinPath
        })
      })

      // Timeout after 5 seconds
      setTimeout(() => {
        testProcess.kill()
        resolve({
          success: false,
          message: 'FRPC binary test timeout',
          path: frpcBinPath
        })
      }, 5000)
    })

  } catch (error) {
    logManager.logApp('error', `FRPC binary test exception: ${error}`)
    return {
      success: false,
      message: `FRPC binary test exception: ${error}`,
      path: getFrpcBinaryPath()
    }
  }
})

// Config Management
ipcMain.handle('config-save', async (_, configPath: string, content: string) => {
  try {
    writeFileSync(configPath, content, 'utf8')
    return { success: true, message: 'Config saved successfully' }
  } catch (error) {
    return { success: false, message: `Failed to save config: ${error}` }
  }
})

ipcMain.handle('config-load', async (_, configPath: string) => {
  try {
    if (!existsSync(configPath)) {
      return { success: false, message: 'Config file not found' }
    }
    const content = readFileSync(configPath, 'utf8')
    return { success: true, content }
  } catch (error) {
    return { success: false, message: `Failed to load config: ${error}` }
  }
})

// File Dialog
ipcMain.handle('show-open-dialog', async (_, options) => {
  const result = await dialog.showOpenDialog(win!, options)
  return result
})

ipcMain.handle('show-save-dialog', async (_, options) => {
  const result = await dialog.showSaveDialog(win!, options)
  return result
})

// Enhanced Config Management
ipcMain.handle('config-get-default-path', async () => {
  return configManager.getConfigPath()
})

ipcMain.handle('config-load-structured', async () => {
  try {
    const config = configManager.loadConfig()
    return { success: true, config }
  } catch (error) {
    logManager.logApp('error', `Failed to load structured config: ${error}`)
    return { success: false, message: `Failed to load config: ${error}` }
  }
})

ipcMain.handle('config-save-structured', async (_, config) => {
  try {
    const success = configManager.saveConfig(config)
    if (success) {
      logManager.logApp('info', 'Configuration saved successfully')
      return { success: true, message: 'Config saved successfully' }
    } else {
      return { success: false, message: 'Failed to save config' }
    }
  } catch (error) {
    logManager.logApp('error', `Failed to save structured config: ${error}`)
    return { success: false, message: `Failed to save config: ${error}` }
  }
})

// Log Management
ipcMain.handle('logs-get-app', async (_, lines = 100) => {
  try {
    const logs = logManager.getAppLogs(lines)
    return { success: true, logs }
  } catch (error) {
    return { success: false, message: `Failed to get app logs: ${error}` }
  }
})

ipcMain.handle('logs-get-frpc', async (_, lines = 100) => {
  try {
    const logs = logManager.getFrpcLogs(lines)
    return { success: true, logs }
  } catch (error) {
    return { success: false, message: `Failed to get frpc logs: ${error}` }
  }
})

ipcMain.handle('logs-clear', async (_, type = 'all') => {
  try {
    const success = logManager.clearLogs(type)
    if (success) {
      logManager.logApp('info', `Cleared ${type} logs`)
      return { success: true, message: 'Logs cleared successfully' }
    } else {
      return { success: false, message: 'Failed to clear logs' }
    }
  } catch (error) {
    logManager.logApp('error', `Failed to clear logs: ${error}`)
    return { success: false, message: `Failed to clear logs: ${error}` }
  }
})

ipcMain.handle('logs-get-paths', async () => {
  try {
    const paths = logManager.getLogPaths()
    return { success: true, paths }
  } catch (error) {
    return { success: false, message: `Failed to get log paths: ${error}` }
  }
})

// Helper function to get FRPC binary path
function getFrpcBinaryPath(): string {
  const platform = process.platform
  const isWindows = platform === 'win32'

  // In development, use the binary from the parent bin directory
  if (!app.isPackaged) {
    // Try multiple possible paths and names
    const basePaths = [
      join(__dirname, '../../../bin'),
      join(__dirname, '../../bin'),
      join(process.cwd(), 'bin'),
      join(process.cwd(), '../bin')
    ]

    // Try different binary names
    const binaryNames = isWindows ? ['frpc.exe', 'frpc'] : ['frpc', 'frpc.exe']

    for (const basePath of basePaths) {
      for (const binaryName of binaryNames) {
        const fullPath = join(basePath, binaryName)
        if (existsSync(fullPath)) {
          logManager.logApp('info', `Found FRPC binary at: ${fullPath}`)
          return fullPath
        }
      }
    }

    // Default fallback
    const defaultPath = join(__dirname, '../../../bin', 'frpc')
    logManager.logApp('warn', `FRPC binary not found, using default path: ${defaultPath}`)
    return defaultPath
  }

  // In production, the binary should be packaged with the app
  const resourcesPath = process.resourcesPath
  const binaryName = isWindows ? 'frpc.exe' : 'frpc'
  const binPath = join(resourcesPath, 'bin', binaryName)

  return binPath
}
