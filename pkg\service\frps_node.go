package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gofrp/fp-multiuser/pkg/database"
	"github.com/gofrp/fp-multiuser/pkg/model"
	"gorm.io/gorm"
)

type FrpsNodeService struct{}

func NewFrpsNodeService() *FrpsNodeService {
	return &FrpsNodeService{}
}

// GetAllNodes returns all frps nodes
func (s *FrpsNodeService) GetAllNodes() ([]model.FrpsNode, error) {
	var nodes []model.FrpsNode
	result := database.DB.Find(&nodes)
	return nodes, result.Error
}

// GetNodeByID returns a frps node by ID
func (s *FrpsNodeService) GetNodeByID(id uint) (*model.FrpsNode, error) {
	var node model.FrpsNode
	result := database.DB.First(&node, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &node, nil
}

// GetNodeByName returns a frps node by name
func (s *FrpsNodeService) GetNodeByName(name string) (*model.FrpsNode, error) {
	var node model.FrpsNode
	result := database.DB.Where("name = ?", name).First(&node)
	if result.Error != nil {
		return nil, result.Error
	}
	return &node, nil
}

// CreateNode creates a new frps node
func (s *FrpsNodeService) CreateNode(node *model.FrpsNode) error {
	// Check if name already exists
	var existingNode model.FrpsNode
	result := database.DB.Where("name = ?", node.Name).First(&existingNode)
	if result.Error == nil {
		return errors.New("node name already exists")
	}
	if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return result.Error
	}

	// Set default values
	if node.Status == "" {
		node.Status = model.NodeStatusUnknown
	}
	if node.AdminPort == 0 {
		node.AdminPort = 7500 // Default frps admin port
	}

	return database.DB.Create(node).Error
}

// UpdateNode updates an existing frps node
func (s *FrpsNodeService) UpdateNode(node *model.FrpsNode) error {
	// Check if the node exists
	var existingNode model.FrpsNode
	if err := database.DB.First(&existingNode, node.ID).Error; err != nil {
		return err
	}

	// Check if name conflicts with other nodes
	if node.Name != existingNode.Name {
		var conflictNode model.FrpsNode
		result := database.DB.Where("name = ? AND id != ?", node.Name, node.ID).First(&conflictNode)
		if result.Error == nil {
			return errors.New("node name already exists")
		}
		if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return result.Error
		}
	}

	return database.DB.Save(node).Error
}

// DeleteNode soft deletes a frps node
func (s *FrpsNodeService) DeleteNode(id uint) error {
	return database.DB.Delete(&model.FrpsNode{}, id).Error
}

// CheckNodeStatus checks the status of a frps node by calling its admin API
func (s *FrpsNodeService) CheckNodeStatus(node *model.FrpsNode) error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Build admin API URL
	adminURL := fmt.Sprintf("http://%s:%d/api/serverinfo", node.Host, node.AdminPort)
	
	req, err := http.NewRequest("GET", adminURL, nil)
	if err != nil {
		node.UpdateStatus(model.NodeStatusError, fmt.Sprintf("Failed to create request: %v", err))
		return database.DB.Save(node).Error
	}

	// Add basic auth if configured
	if node.AdminUser != "" && node.AdminPwd != "" {
		req.SetBasicAuth(node.AdminUser, node.AdminPwd)
	}

	resp, err := client.Do(req)
	if err != nil {
		node.UpdateStatus(model.NodeStatusOffline, fmt.Sprintf("Connection failed: %v", err))
		return database.DB.Save(node).Error
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		node.UpdateStatus(model.NodeStatusError, fmt.Sprintf("HTTP %d: %s", resp.StatusCode, resp.Status))
		return database.DB.Save(node).Error
	}

	// Parse response to get server info
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		node.UpdateStatus(model.NodeStatusError, fmt.Sprintf("Failed to read response: %v", err))
		return database.DB.Save(node).Error
	}

	var serverInfo map[string]interface{}
	if err := json.Unmarshal(body, &serverInfo); err != nil {
		node.UpdateStatus(model.NodeStatusError, fmt.Sprintf("Failed to parse response: %v", err))
		return database.DB.Save(node).Error
	}

	// Update node information
	node.UpdateStatus(model.NodeStatusOnline, "")
	
	if version, ok := serverInfo["version"].(string); ok {
		node.Version = version
	}
	if uptime, ok := serverInfo["uptime"].(float64); ok {
		node.Uptime = int64(uptime)
	}
	if clientCount, ok := serverInfo["client_counts"].(float64); ok {
		node.ClientCount = int(clientCount)
	}
	if proxyCount, ok := serverInfo["proxy_counts"].(float64); ok {
		node.ProxyCount = int(proxyCount)
	}

	return database.DB.Save(node).Error
}

// CheckAllNodesStatus checks the status of all active nodes
func (s *FrpsNodeService) CheckAllNodesStatus() error {
	var nodes []model.FrpsNode
	if err := database.DB.Where("is_active = ?", true).Find(&nodes).Error; err != nil {
		return err
	}

	for i := range nodes {
		if err := s.CheckNodeStatus(&nodes[i]); err != nil {
			// Log error but continue checking other nodes
			fmt.Printf("Error checking node %s: %v\n", nodes[i].Name, err)
		}
	}

	return nil
}

// GetNodeStats returns statistics for a specific node
func (s *FrpsNodeService) GetNodeStats(nodeID uint, days int) ([]model.FrpsNodeStats, error) {
	var stats []model.FrpsNodeStats
	
	startDate := time.Now().AddDate(0, 0, -days)
	result := database.DB.Where("node_id = ? AND recorded_at >= ?", nodeID, startDate).
		Order("recorded_at DESC").
		Find(&stats)
	
	return stats, result.Error
}

// RecordNodeStats records current statistics for a node
func (s *FrpsNodeService) RecordNodeStats(node *model.FrpsNode) error {
	stats := model.FrpsNodeStats{
		NodeID:          node.ID,
		ClientCount:     node.ClientCount,
		ProxyCount:      node.ProxyCount,
		TotalTrafficIn:  0, // Would need to get from frps API
		TotalTrafficOut: 0, // Would need to get from frps API
		CurConns:        0, // Would need to get from frps API
		Uptime:          node.Uptime,
		RecordedAt:      time.Now(),
	}

	return database.DB.Create(&stats).Error
}

// GetActiveNodes returns all active nodes
func (s *FrpsNodeService) GetActiveNodes() ([]model.FrpsNode, error) {
	var nodes []model.FrpsNode
	result := database.DB.Where("is_active = ?", true).Find(&nodes)
	return nodes, result.Error
}

// ToggleNodeStatus toggles the active status of a node
func (s *FrpsNodeService) ToggleNodeStatus(id uint) error {
	var node model.FrpsNode
	if err := database.DB.First(&node, id).Error; err != nil {
		return err
	}

	node.IsActive = !node.IsActive
	return database.DB.Save(&node).Error
}

// GetNodeDashboardStats returns dashboard statistics for all nodes
func (s *FrpsNodeService) GetNodeDashboardStats() (map[string]interface{}, error) {
	var totalNodes int64
	var onlineNodes int64
	var offlineNodes int64
	var totalClients int64
	var totalProxies int64

	database.DB.Model(&model.FrpsNode{}).Where("is_active = ?", true).Count(&totalNodes)
	database.DB.Model(&model.FrpsNode{}).Where("is_active = ? AND status = ?", true, model.NodeStatusOnline).Count(&onlineNodes)
	database.DB.Model(&model.FrpsNode{}).Where("is_active = ? AND status = ?", true, model.NodeStatusOffline).Count(&offlineNodes)

	// Sum up client and proxy counts
	var nodes []model.FrpsNode
	database.DB.Where("is_active = ? AND status = ?", true, model.NodeStatusOnline).Find(&nodes)
	for _, node := range nodes {
		totalClients += int64(node.ClientCount)
		totalProxies += int64(node.ProxyCount)
	}

	return map[string]interface{}{
		"total_nodes":   totalNodes,
		"online_nodes":  onlineNodes,
		"offline_nodes": offlineNodes,
		"total_clients": totalClients,
		"total_proxies": totalProxies,
	}, nil
}
